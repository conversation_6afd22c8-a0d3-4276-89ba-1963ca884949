import 'package:flutter/material.dart';
import 'package:stillpoint/widgets/mood_tracker.dart';
import 'package:stillpoint/widgets/quick_chat_dialog.dart';
import 'package:stillpoint/screens/journal_screen.dart';
import 'package:stillpoint/screens/counselor_screen.dart';
import 'package:stillpoint/screens/resources_screen.dart';

class DashboardScreen extends StatefulWidget {
  final String userName;
  final List<String> selectedAreas;

  const DashboardScreen({
    super.key,
    required this.userName,
    required this.selectedAreas,
  });

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good morning, ${widget.userName}!';
    } else if (hour < 17) {
      return 'Good afternoon, ${widget.userName}!';
    } else {
      return 'Good evening, ${widget.userName}!';
    }
  }

  String _getTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'morning';
    } else if (hour < 17) {
      return 'afternoon';
    } else {
      return 'evening';
    }
  }

  IconData _getTimeIcon() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return Icons.wb_sunny_outlined;
    } else if (hour < 17) {
      return Icons.wb_sunny;
    } else {
      return Icons.nights_stay_outlined;
    }
  }

  void _showQuickChat() {
    showDialog(context: context, builder: (context) => const QuickChatDialog());
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF6366F1).withValues(alpha: 0.15), // Vibrant indigo
              const Color(0xFF8B5CF6).withValues(alpha: 0.12), // Purple
              const Color(0xFF06B6D4).withValues(alpha: 0.08), // Cyan
              colorScheme.surface,
            ],
            stops: const [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Modern Greeting Card with floating elements
                    Stack(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(28),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF667EEA), // Vibrant blue-purple
                                const Color(0xFF764BA2), // Deep purple
                                const Color(0xFF6B73FF), // Electric blue
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(28),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(
                                  0xFF667EEA,
                                ).withValues(alpha: 0.4),
                                blurRadius: 25,
                                offset: const Offset(0, 12),
                                spreadRadius: 2,
                              ),
                              BoxShadow(
                                color: const Color(
                                  0xFF764BA2,
                                ).withValues(alpha: 0.2),
                                blurRadius: 40,
                                offset: const Offset(0, 20),
                                spreadRadius: -5,
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withValues(
                                        alpha: 0.2,
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: Icon(
                                      _getTimeIcon(),
                                      color: Colors.white,
                                      size: 28,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _getGreeting(),
                                          style: theme.textTheme.headlineSmall
                                              ?.copyWith(
                                                color: Colors.white,
                                                fontWeight: FontWeight.w700,
                                              ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          'Ready to nurture your wellbeing?',
                                          style: theme.textTheme.bodyMedium
                                              ?.copyWith(
                                                color: Colors.white.withValues(
                                                  alpha: 0.9,
                                                ),
                                              ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        // Floating decorative elements with glow effects
                        Positioned(
                          top: 15,
                          right: 15,
                          child: Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  Colors.white.withValues(alpha: 0.3),
                                  Colors.white.withValues(alpha: 0.1),
                                  Colors.transparent,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(40),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  blurRadius: 15,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 25,
                          right: 35,
                          child: Container(
                            width: 45,
                            height: 45,
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFF00F5FF,
                                  ).withValues(alpha: 0.4),
                                  const Color(
                                    0xFF00F5FF,
                                  ).withValues(alpha: 0.1),
                                  Colors.transparent,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(22.5),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(
                                    0xFF00F5FF,
                                  ).withValues(alpha: 0.3),
                                  blurRadius: 12,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          top: 45,
                          left: 25,
                          child: Container(
                            width: 35,
                            height: 35,
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  const Color(
                                    0xFFFF6B9D,
                                  ).withValues(alpha: 0.5),
                                  const Color(
                                    0xFFFF6B9D,
                                  ).withValues(alpha: 0.2),
                                  Colors.transparent,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(17.5),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(
                                    0xFFFF6B9D,
                                  ).withValues(alpha: 0.3),
                                  blurRadius: 10,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Compact Mood Tracker
                    MoodTracker(timeOfDay: _getTimeOfDay()),

                    const SizedBox(height: 24),

                    // Modern Wellness Tools Section
                    Row(
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          color: colorScheme.primary,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Your Wellness Tools',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.0,
                      children: [
                        _buildActionCard(
                          context,
                          'Journal',
                          Icons.book_outlined,
                          colorScheme.primary,
                          'Reflect and write',
                          () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const JournalScreen(),
                            ),
                          ),
                        ),
                        _buildActionCard(
                          context,
                          'AI Counselor',
                          Icons.psychology_outlined,
                          colorScheme.secondary,
                          'Start a session',
                          () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => CounselorScreen(
                                    userName: widget.userName,
                                  ),
                            ),
                          ),
                        ),
                        _buildActionCard(
                          context,
                          'Resources',
                          Icons.library_books_outlined,
                          colorScheme.tertiary,
                          'Blogs & worksheets',
                          () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => ResourcesScreen(
                                    selectedAreas: widget.selectedAreas,
                                  ),
                            ),
                          ),
                        ),
                        _buildActionCard(
                          context,
                          'Quick Chat',
                          Icons.chat_bubble_outline,
                          colorScheme.primary,
                          'Instant support',
                          _showQuickChat,
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Mental Health Resources Section
                    _buildMentalHealthResourcesSection(
                      context,
                      theme,
                      colorScheme,
                    ),

                    const SizedBox(height: 32),

                    // Modern Inspirational Quote Card
                    Container(
                      padding: const EdgeInsets.all(28),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(
                              0xFFFFB347,
                            ).withValues(alpha: 0.2), // Warm orange
                            const Color(
                              0xFFFFCC70,
                            ).withValues(alpha: 0.15), // Light peach
                            const Color(
                              0xFFFFF8DC,
                            ).withValues(alpha: 0.1), // Cream
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          stops: const [0.0, 0.5, 1.0],
                        ),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: const Color(0xFFFFB347).withValues(alpha: 0.4),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFFFFB347,
                            ).withValues(alpha: 0.25),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                            spreadRadius: 2,
                          ),
                          BoxShadow(
                            color: const Color(
                              0xFFFFCC70,
                            ).withValues(alpha: 0.15),
                            blurRadius: 30,
                            offset: const Offset(0, 20),
                            spreadRadius: -5,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color(
                                    0xFFFFB347,
                                  ).withValues(alpha: 0.4),
                                  const Color(
                                    0xFFFFCC70,
                                  ).withValues(alpha: 0.3),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: const Color(
                                    0xFFFFB347,
                                  ).withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.auto_awesome,
                              color: const Color(0xFFFF8C00),
                              size: 24,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            '"Every moment is a fresh beginning."',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: colorScheme.onSurface,
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '— T.S. Eliot',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String subtitle,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);

    // Enhanced color palette for each card
    final Map<String, List<Color>> cardGradients = {
      'Journal': [const Color(0xFF667EEA), const Color(0xFF764BA2)],
      'AI Counselor': [const Color(0xFFFF6B9D), const Color(0xFFC44569)],
      'Resources': [const Color(0xFF00C9FF), const Color(0xFF92FE9D)],
      'Quick Chat': [const Color(0xFFFC466B), const Color(0xFF3F5EFB)],
    };

    final gradientColors =
        cardGradients[title] ?? [color, color.withValues(alpha: 0.7)];

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              gradientColors[0].withValues(alpha: 0.15),
              gradientColors[1].withValues(alpha: 0.08),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: gradientColors[0].withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: gradientColors[0].withValues(alpha: 0.25),
              blurRadius: 20,
              offset: const Offset(0, 8),
              spreadRadius: 2,
            ),
            BoxShadow(
              color: gradientColors[1].withValues(alpha: 0.15),
              blurRadius: 30,
              offset: const Offset(0, 15),
              spreadRadius: -5,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    gradientColors[0].withValues(alpha: 0.3),
                    gradientColors[1].withValues(alpha: 0.2),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: gradientColors[0].withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(icon, size: 36, color: gradientColors[0]),
            ),
            const SizedBox(height: 18),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: gradientColors[0],
                fontWeight: FontWeight.w800,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: gradientColors[0].withValues(alpha: 0.8),
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMentalHealthResourcesSection(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final mentalHealthResources = [
      {
        'title': 'Crisis Support',
        'description': '24/7 mental health crisis support',
        'resources': [
          {
            'name': '988 Suicide & Crisis Lifeline',
            'info': 'Call or text 988 for immediate support',
          },
          {'name': 'Crisis Text Line', 'info': 'Text HOME to 741741'},
          {
            'name': 'SAMHSA National Helpline',
            'info': '1-************ for treatment referrals',
          },
        ],
        'icon': Icons.support_agent,
        'color': colorScheme.primary,
      },
      {
        'title': 'Mental Health Resources',
        'description': 'Professional mental health support',
        'resources': [
          {
            'name': 'Psychology Today',
            'info': 'Find therapists and mental health professionals',
          },
          {
            'name': 'NAMI (National Alliance on Mental Illness)',
            'info': 'Mental health education and support',
          },
          {
            'name': 'Mental Health America',
            'info': 'Screening tools and resources',
          },
        ],
        'icon': Icons.psychology_outlined,
        'color': colorScheme.secondary,
      },
      {
        'title': 'Self-Care & Wellness',
        'description': 'Tools for daily mental wellness',
        'resources': [
          {
            'name': 'Mindfulness Apps',
            'info': 'Headspace, Calm, Insight Timer',
          },
          {'name': 'Wellness Websites', 'info': 'Mental Health America, NIMH'},
          {
            'name': 'Support Groups',
            'info': 'Online and local community support',
          },
        ],
        'icon': Icons.self_improvement,
        'color': colorScheme.tertiary,
      },
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.favorite_outline,
                  color: colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Mental Health Resources',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      'Professional support and wellness resources',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Mental Health Resource Cards
          ...mentalHealthResources.map(
            (resource) => _buildMentalHealthResourceCard(
              context,
              resource,
              theme,
              colorScheme,
            ),
          ),

          const SizedBox(height: 16),

          // View All Resources Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showAllMentalHealthResources(context),
              icon: Icon(
                Icons.library_books_outlined,
                size: 18,
                color: colorScheme.primary,
              ),
              label: Text(
                'View All Mental Health Resources',
                style: TextStyle(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: BorderSide(
                  color: colorScheme.primary.withValues(alpha: 0.3),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMentalHealthResourceCard(
    BuildContext context,
    Map<String, dynamic> resource,
    ThemeData theme,
    ColorScheme colorScheme,
  ) {
    final cardColor = resource['color'] as Color;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: cardColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  resource['icon'] as IconData,
                  color: cardColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      resource['title'] as String,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: cardColor,
                      ),
                    ),
                    Text(
                      resource['description'] as String,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...((resource['resources'] as List).map(
            (res) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 4,
                    height: 4,
                    margin: const EdgeInsets.only(top: 6, right: 8),
                    decoration: BoxDecoration(
                      color: cardColor.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          res['name'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          res['info'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )),
        ],
      ),
    );
  }

  void _showAllMentalHealthResources(BuildContext context) {
    final allResources = [
      {
        'category': 'Crisis Support',
        'resources': [
          {
            'name': '988 Suicide & Crisis Lifeline',
            'info': 'Call or text 988 for immediate support',
            'type': 'Crisis',
          },
          {
            'name': 'Crisis Text Line',
            'info': 'Text HOME to 741741',
            'type': 'Crisis',
          },
          {
            'name': 'SAMHSA National Helpline',
            'info': '1-************ for treatment referrals',
            'type': 'Treatment',
          },
          {
            'name': 'National Domestic Violence Hotline',
            'info': '1-************',
            'type': 'Support',
          },
        ],
      },
      {
        'category': 'Professional Help',
        'resources': [
          {
            'name': 'Psychology Today',
            'info': 'Find therapists and mental health professionals',
            'type': 'Directory',
          },
          {
            'name': 'NAMI (National Alliance on Mental Illness)',
            'info': 'Mental health education and support',
            'type': 'Education',
          },
          {
            'name': 'Mental Health America',
            'info': 'Screening tools and resources',
            'type': 'Screening',
          },
          {
            'name': 'BetterHelp',
            'info': 'Online therapy platform',
            'type': 'Therapy',
          },
        ],
      },
      {
        'category': 'Self-Care & Wellness',
        'resources': [
          {
            'name': 'Headspace',
            'info': 'Meditation and mindfulness app',
            'type': 'App',
          },
          {
            'name': 'Calm',
            'info': 'Sleep stories and meditation',
            'type': 'App',
          },
          {
            'name': 'Insight Timer',
            'info': 'Free meditation app',
            'type': 'App',
          },
          {
            'name': 'NIMH (National Institute of Mental Health)',
            'info': 'Mental health information and research',
            'type': 'Information',
          },
        ],
      },
    ];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Mental Health Resources',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Professional support and wellness tools',
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 20),
                    Expanded(
                      child: ListView.builder(
                        controller: scrollController,
                        itemCount: allResources.length,
                        itemBuilder: (context, categoryIndex) {
                          final category = allResources[categoryIndex];
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                ),
                                child: Text(
                                  category['category'] as String,
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color:
                                        Theme.of(context).colorScheme.secondary,
                                  ),
                                ),
                              ),
                              ...((category['resources'] as List).map(
                                (resource) => Container(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color:
                                        Theme.of(context).colorScheme.surface,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .outline
                                          .withValues(alpha: 0.2),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              resource['name'] as String,
                                              style: Theme.of(
                                                context,
                                              ).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            const SizedBox(height: 2),
                                            Text(
                                              resource['info'] as String,
                                              style: Theme.of(
                                                context,
                                              ).textTheme.bodySmall?.copyWith(
                                                color:
                                                    Theme.of(context)
                                                        .colorScheme
                                                        .onSurfaceVariant,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primaryContainer,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Text(
                                          resource['type'] as String,
                                          style: Theme.of(
                                            context,
                                          ).textTheme.bodySmall?.copyWith(
                                            color:
                                                Theme.of(context)
                                                    .colorScheme
                                                    .onPrimaryContainer,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )),
                              const SizedBox(height: 16),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }
}
